# CodingBuddy Chat Interface

This document describes the interactive chat interface for CodingBuddy, which transforms the plugin from a simple menu-driven tool into an interactive AI coding assistant similar to Cline/Augment.

## Overview

The chat interface provides:
- **Persistent conversation history** with automatic saving
- **Interactive chat sessions** with context preservation
- **Multiple conversation management** with history browsing
- **Integration with existing AI connector** for seamless AI interactions
- **Command-based interface** for advanced functionality

## Architecture

### Core Components

1. **conversation_manager.lua** - Handles conversation state and persistence
2. **chat_interface.lua** - Provides the user interface and interaction logic
3. **Integration with ai_connector.lua** - Leverages existing AI capabilities
4. **JSON-based storage** - Conversations saved as JSON files

### File Structure
```
~/.config/geany/plugins/geanylua/codingbuddy/
├── conversations/           # Conversation history storage
│   ├── conv_1234567890_1234.json
│   ├── conv_1234567891_5678.json
│   └── ...
├── logs/                   # Existing logs directory
└── cache/                  # Existing cache directory
```

## Usage

### Opening the Chat Interface

From Geany's Tools menu:
- **CodingBuddy: Open Chat** - Opens the interactive chat interface
- **CodingBuddy: Analyze with Chat** - Analyzes current buffer using chat interface
- **CodingBuddy: Analyze Buffer** - Original simple analysis (still available)

### Chat Commands

Within the chat interface, you can use these commands:

- **Regular messages** - Just type your question or request
- **/new** - Start a new conversation
- **/history** - View list of recent conversations
- **/quit** or **/exit** - Close the chat interface

### Example Chat Session

```
=== New Conversation ===
Messages: 0 | Tokens: 0 | Cost: $0.0000

--------------------------------------------------
What can I do for you?

Commands:
- Type your message and press OK to send
- Type '/new' to start a new conversation
- Type '/history' to see conversation list
- Type '/quit' to close chat

Your message: Can you help me optimize this Python function?

[12:34] You: Can you help me optimize this Python function?
[12:34] Assistant: I'd be happy to help optimize your Python function! Could you please share the function code you'd like me to review?

Your message: def find_max(numbers):
    max_val = numbers[0]
    for num in numbers:
        if num > max_val:
            max_val = num
    return max_val

[12:35] You: def find_max(numbers): ...
[12:35] Assistant: I can see a few ways to optimize this function:

1. **Use built-in max() function** (most Pythonic):
   ```python
   def find_max(numbers):
       return max(numbers)
   ```

2. **Handle edge cases** (empty list):
   ```python
   def find_max(numbers):
       if not numbers:
           raise ValueError("Cannot find max of empty list")
       return max(numbers)
   ```

3. **If you must use a loop** (for learning purposes):
   ```python
   def find_max(numbers):
       if not numbers:
           raise ValueError("Cannot find max of empty list")
       max_val = numbers[0]
       for num in numbers[1:]:  # Start from index 1
           if num > max_val:
               max_val = num
       return max_val
   ```

The built-in `max()` function is optimized in C and will be much faster than any Python loop.
```

## Technical Implementation

### Conversation Management

Each conversation is stored as a JSON file with this structure:

```json
{
  "id": "conv_1234567890_1234",
  "title": "Python function optimization...",
  "created_at": "2024-01-01T12:00:00Z",
  "updated_at": "2024-01-01T12:30:00Z",
  "messages": [
    {
      "role": "user",
      "content": "Can you help me optimize this Python function?",
      "timestamp": "2024-01-01T12:00:00Z",
      "tokens": {},
      "cost": 0
    },
    {
      "role": "assistant", 
      "content": "I'd be happy to help optimize your Python function!...",
      "timestamp": "2024-01-01T12:00:15Z",
      "tokens": {"prompt_tokens": 25, "completion_tokens": 150, "total_tokens": 175},
      "cost": 0.001234
    }
  ],
  "metadata": {
    "total_tokens": 175,
    "total_cost": 0.001234,
    "model_used": "claude-3-sonnet",
    "provider": "anthropic"
  }
}
```

### UI Limitations and Solutions

Due to GeanyLua's limited GTK bindings, the current implementation uses:

1. **geany.input()** dialogs for user input
2. **geany.message()** for displaying responses
3. **Text-based conversation display** with timestamps
4. **Command-based navigation** for advanced features

### Future Enhancements

When more advanced GTK bindings become available:

1. **Dedicated chat window** with proper text areas
2. **Syntax highlighting** for code responses
3. **Real-time typing indicators** during AI processing
4. **Sidebar integration** within Geany's interface
5. **Rich text formatting** for better readability

## API Reference

### conversation_manager.lua

```lua
-- Create new conversation
local conv = conversation_manager.create_new_conversation()

-- Add messages
conversation_manager.add_message('user', 'Hello', metadata)
conversation_manager.add_message('assistant', 'Hi there!', metadata)

-- Get conversation context
local context = conversation_manager.get_conversation_context(10)

-- Save/load conversations
conversation_manager.save_current_conversation()
conversation_manager.load_conversation(conversation_id)

-- List conversations
local conversations = conversation_manager.list_conversations()

-- Get statistics
local stats = conversation_manager.get_conversation_stats()
```

### chat_interface.lua

```lua
-- Open interactive chat
chat_interface.open_chat()

-- Quick single interaction
chat_interface.quick_chat("Analyze this code: ...")

-- Analyze current buffer with chat
chat_interface.analyze_current_buffer_with_chat()

-- Get chat status
local status = chat_interface.get_chat_status()
```

## Testing

Run the test suite to verify functionality:

```bash
cd codingbuddy
lua test_chat_interface.lua
```

Or test individual components:

```lua
local test = require('codingbuddy.test_chat_interface')
test.test_conversation_manager()
test.test_chat_interface_components()
```

## Configuration

The chat interface uses the existing CodingBuddy configuration system. Ensure you have:

1. **API keys configured** in `config.json` or environment variables
2. **Proper directory permissions** for conversation storage
3. **Required Lua libraries** (lua-socket, luasec recommended)

## Troubleshooting

### Common Issues

1. **"No response received"** - Check API key configuration
2. **"Could not create conversations directory"** - Check file permissions
3. **"JSON decode error"** - Conversation file may be corrupted
4. **Chat interface not opening** - Ensure GeanyLua plugin is loaded

### Debug Mode

Enable debug logging by setting environment variable:
```bash
export CODINGBUDDY_DEBUG=1
```

This will provide detailed logging in `~/.config/geany/plugins/geanylua/codingbuddy/logs/codingbuddy.log`.

## Migration from Simple Interface

The chat interface is fully backward compatible. Existing functionality remains available:

- **Tools > CodingBuddy: Analyze Buffer** - Original simple analysis
- **All existing configuration** - No changes needed
- **Existing cache and logs** - Preserved and enhanced

New users can start directly with the chat interface for the full interactive experience.
