-- utils.lua - helpers
local M = {}

function M.sha1(s)
  -- try to use openssl via io.popen for simplicity (dev env)
  local f = io.popen("printf '%s' '"..s:gsub("'","'\\''").."' | sha1sum 2>/dev/null | awk '{print $1}'")
  if f then
    local out = f:read('*l')
    f:close()
    return out
  end
  return tostring(#s)..'_'..string.sub(s,1,8)
end

function M.write_file(path, content)
  local dir = path:match('^(.+)/[^/]+$')
  if dir then os.execute("mkdir -p '"..dir.."'") end
  local f, err = io.open(path, 'w')
  if not f then return nil, err end
  f:write(content)
  f:close()
  return true
end

function M.append_file(path, content)
  local dir = path:match('^(.+)/[^/]+$')
  if dir then os.execute("mkdir -p '"..dir.."'") end
  local f, err = io.open(path, 'a')
  if not f then return nil, err end
  f:write(content)
  f:close()
  return true
end

function M.read_file(path)
  local f = io.open(path, 'r')
  if not f then return nil end
  local c = f:read('*a')
  f:close()
  return c
end

function M.expanduser(path)
  local home = os.getenv('HOME') or ''
  return path:gsub('^~', home)
end

function M.log(tag, message)
  local home = os.getenv('HOME') or '.'
  local base = home..'/.config/geany/plugins/geanylua/codingbuddy/logs'
  local ts = os.date('!%Y-%m-%dT%H:%M:%SZ')
  local line = string.format('[%s] %s: %s\n', ts, tag or 'LOG', tostring(message))
  M.append_file(base..'/codingbuddy.log', line)
end

-- Simple JSON encoding/decoding fallback (when dkjson is not available)
function M.simple_json_encode(obj)
  local function encode_value(val)
    local t = type(val)
    if t == 'string' then
      return '"' .. val:gsub('\\', '\\\\'):gsub('"', '\\"'):gsub('\n', '\\n'):gsub('\r', '\\r'):gsub('\t', '\\t') .. '"'
    elseif t == 'number' then
      return tostring(val)
    elseif t == 'boolean' then
      return val and 'true' or 'false'
    elseif t == 'nil' then
      return 'null'
    elseif t == 'table' then
      -- Check if it's an array
      local is_array = true
      local max_idx = 0
      for k, _ in pairs(val) do
        if type(k) ~= 'number' or k <= 0 or k ~= math.floor(k) then
          is_array = false
          break
        end
        max_idx = math.max(max_idx, k)
      end

      if is_array then
        local parts = {}
        for i = 1, max_idx do
          table.insert(parts, encode_value(val[i]))
        end
        return '[' .. table.concat(parts, ',') .. ']'
      else
        local parts = {}
        for k, v in pairs(val) do
          table.insert(parts, encode_value(tostring(k)) .. ':' .. encode_value(v))
        end
        return '{' .. table.concat(parts, ',') .. '}'
      end
    else
      return 'null'
    end
  end

  return encode_value(obj)
end

function M.simple_json_decode(str)
  -- Very basic JSON decoder - only handles simple cases
  -- For production use, dkjson should be preferred
  if not str or str == '' then
    return nil
  end

  -- Remove whitespace
  str = str:gsub('^%s+', ''):gsub('%s+$', '')

  -- Try to load as Lua table (unsafe but simple)
  -- Convert JSON syntax to Lua syntax
  local lua_str = str
    :gsub('null', 'nil')
    :gsub('true', 'true')
    :gsub('false', 'false')
    :gsub('"([^"]*)":', '["%1"]=')  -- Convert "key": to ["key"]=

  local func = load('return ' .. lua_str)
  if func then
    local ok, result = pcall(func)
    if ok then
      return result
    end
  end

  return nil
end

return M

