-- conversation_manager.lua - Manages chat conversations and history
-- Handles conversation state, history persistence, and session management

local utils = require('codingbuddy.utils')
local config = require('codingbuddy.config')

local M = {}

-- Current conversation state
local current_conversation = nil
local conversation_history = {}

-- Conversation structure:
-- {
--   id = "unique_conversation_id",
--   title = "Auto-generated or user-set title",
--   created_at = "2024-01-01T12:00:00Z",
--   updated_at = "2024-01-01T12:30:00Z",
--   messages = {
--     { role = "user", content = "Hello", timestamp = "...", tokens = {...} },
--     { role = "assistant", content = "Hi there!", timestamp = "...", tokens = {...} },
--     ...
--   },
--   metadata = {
--     total_tokens = 150,
--     total_cost = 0.001234,
--     model_used = "gpt-4",
--     provider = "openai"
--   }
-- }

local function get_conversations_dir()
  local home = os.getenv('HOME') or '.'
  return home .. '/.config/geany/plugins/geanylua/codingbuddy/conversations'
end

local function ensure_conversations_dir()
  local dir = get_conversations_dir()
  local ok = os.execute('mkdir -p "' .. dir .. '"')
  return ok == 0 or ok == true  -- Different Lua versions return different values
end

local function generate_conversation_id()
  -- Generate a unique ID based on timestamp and random component
  local timestamp = os.time()
  local random = math.random(1000, 9999)
  return string.format("conv_%d_%d", timestamp, random)
end

local function generate_conversation_title(first_message)
  -- Generate a title from the first user message
  if not first_message or not first_message.content then
    return "New Conversation"
  end
  
  local content = first_message.content
  -- Take first 50 characters and clean up
  local title = content:sub(1, 50):gsub('\n', ' '):gsub('%s+', ' ')
  if #content > 50 then
    title = title .. "..."
  end
  
  return title
end

function M.create_new_conversation()
  local conv_id = generate_conversation_id()
  local now = os.date('!%Y-%m-%dT%H:%M:%SZ')
  
  current_conversation = {
    id = conv_id,
    title = "New Conversation",
    created_at = now,
    updated_at = now,
    messages = {},
    metadata = {
      total_tokens = 0,
      total_cost = 0,
      model_used = nil,
      provider = nil
    }
  }
  
  return current_conversation
end

function M.get_current_conversation()
  if not current_conversation then
    M.create_new_conversation()
  end
  return current_conversation
end

function M.add_message(role, content, metadata)
  local conv = M.get_current_conversation()
  local now = os.date('!%Y-%m-%dT%H:%M:%SZ')
  
  local message = {
    role = role,  -- "user" or "assistant"
    content = content,
    timestamp = now,
    tokens = metadata and metadata.tokens or {},
    cost = metadata and metadata.cost or 0
  }
  
  table.insert(conv.messages, message)
  conv.updated_at = now
  
  -- Update conversation title if this is the first user message
  if role == "user" and #conv.messages == 1 then
    conv.title = generate_conversation_title(message)
  end
  
  -- Update metadata
  if metadata then
    if metadata.tokens and metadata.tokens.total then
      conv.metadata.total_tokens = conv.metadata.total_tokens + metadata.tokens.total
    end
    if metadata.cost then
      conv.metadata.total_cost = conv.metadata.total_cost + metadata.cost
    end
    if metadata.model then
      conv.metadata.model_used = metadata.model
    end
    if metadata.provider then
      conv.metadata.provider = metadata.provider
    end
  end
  
  -- Auto-save after adding message
  M.save_current_conversation()
  
  return message
end

function M.get_conversation_context(max_messages)
  local conv = M.get_current_conversation()
  max_messages = max_messages or 20  -- Default to last 20 messages
  
  local messages = conv.messages
  local start_idx = math.max(1, #messages - max_messages + 1)
  
  local context = {}
  for i = start_idx, #messages do
    table.insert(context, {
      role = messages[i].role,
      content = messages[i].content
    })
  end
  
  return context
end

function M.save_current_conversation()
  if not current_conversation then
    return false, "No current conversation to save"
  end
  
  if not ensure_conversations_dir() then
    return false, "Could not create conversations directory"
  end
  
  local filepath = get_conversations_dir() .. '/' .. current_conversation.id .. '.json'
  
  -- Try to use dkjson if available, otherwise create simple JSON
  local json_str
  local ok, dkjson = pcall(require, 'dkjson')
  if ok and dkjson then
    json_str = dkjson.encode(current_conversation, { indent = true })
  else
    -- Fallback: create simple JSON manually
    json_str = utils.simple_json_encode(current_conversation)
  end
  
  if not json_str then
    return false, "Could not encode conversation to JSON"
  end
  
  local file = io.open(filepath, 'w')
  if not file then
    return false, "Could not open file for writing: " .. filepath
  end
  
  file:write(json_str)
  file:close()
  
  return true
end

function M.load_conversation(conversation_id)
  local filepath = get_conversations_dir() .. '/' .. conversation_id .. '.json'
  
  local file = io.open(filepath, 'r')
  if not file then
    return nil, "Could not open conversation file: " .. filepath
  end
  
  local content = file:read('*all')
  file:close()
  
  if not content or content == '' then
    return nil, "Empty conversation file"
  end
  
  -- Try to parse JSON
  local conversation
  local ok, dkjson = pcall(require, 'dkjson')
  if ok and dkjson then
    local err
    conversation, err = dkjson.decode(content)
    if not conversation then
      return nil, "JSON decode error: " .. tostring(err)
    end
  else
    -- Fallback: try simple JSON parsing
    conversation = utils.simple_json_decode(content)
    if not conversation then
      return nil, "Could not parse JSON (no dkjson available)"
    end
  end
  
  current_conversation = conversation
  return conversation
end

function M.list_conversations()
  if not ensure_conversations_dir() then
    return {}
  end
  
  local conversations = {}
  local dir = get_conversations_dir()
  
  -- Use ls to list JSON files (cross-platform alternative would be better)
  local handle = io.popen('ls "' .. dir .. '"/*.json 2>/dev/null')
  if not handle then
    return conversations
  end
  
  for filename in handle:lines() do
    local conv_id = filename:match('([^/]+)%.json$')
    if conv_id then
      -- Load basic info (title, timestamps) without full content
      local filepath = dir .. '/' .. filename
      local file = io.open(filepath, 'r')
      if file then
        local content = file:read('*all')
        file:close()
        
        -- Try to extract just the metadata we need
        local title = content:match('"title"%s*:%s*"([^"]*)"')
        local created_at = content:match('"created_at"%s*:%s*"([^"]*)"')
        local updated_at = content:match('"updated_at"%s*:%s*"([^"]*)"')
        
        if title and created_at then
          table.insert(conversations, {
            id = conv_id,
            title = title,
            created_at = created_at,
            updated_at = updated_at or created_at
          })
        end
      end
    end
  end
  handle:close()
  
  -- Sort by updated_at (most recent first)
  table.sort(conversations, function(a, b)
    return (a.updated_at or '') > (b.updated_at or '')
  end)
  
  return conversations
end

function M.delete_conversation(conversation_id)
  local filepath = get_conversations_dir() .. '/' .. conversation_id .. '.json'
  local ok = os.remove(filepath)
  
  -- If this was the current conversation, clear it
  if current_conversation and current_conversation.id == conversation_id then
    current_conversation = nil
  end
  
  return ok ~= nil
end

function M.get_conversation_stats()
  local conv = M.get_current_conversation()
  return {
    message_count = #conv.messages,
    total_tokens = conv.metadata.total_tokens,
    total_cost = conv.metadata.total_cost,
    created_at = conv.created_at,
    updated_at = conv.updated_at
  }
end

-- Initialize random seed
math.randomseed(os.time())

return M
