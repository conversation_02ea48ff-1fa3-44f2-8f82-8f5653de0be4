{"provider": "openrouter", "fallback_chain": ["openrouter", "openai", "anthropic", "deepseek"], "task_models": {"analysis": "anthropic/claude-3.5-sonnet"}, "provider_models": {"openrouter": "anthropic/claude-3.5-sonnet", "openai": "gpt-4o-mini", "anthropic": "claude-3-5-sonnet-latest", "deepseek": "deepseek-chat"}, "openrouter_api_key": "YOUR_OPENROUTER_API_KEY", "openai_api_key": "YOUR_OPENAI_API_KEY", "anthropic_api_key": "YOUR_ANTHROPIC_API_KEY", "deepseek_api_key": "YOUR_DEEPSEEK_API_KEY", "cache_enabled": true, "log_enabled": true, "timeout": 30, "cost": {"enabled": true, "currency": "USD", "prices_per_1k": {"openai": {"default_input": 0.003, "default_output": 0.006}, "anthropic": {"default_input": 0.003, "default_output": 0.015}, "openrouter": {"default_input": 0.0, "default_output": 0.0}, "deepseek": {"default_input": 0.002, "default_output": 0.002}}}, "notes": {"provider": "Which provider to prefer initially.", "fallback_chain": "Order of providers to try when requests fail.", "task_models": "Preferred model per task type (analysis/generation/docs/refactor).", "provider_models": "Default model to use for each provider if task model is ambiguous.", "*_api_key": "You can set via environment variables instead of here.", "cache_enabled": "Caches analysis results by code hash in cache/.", "log_enabled": "Writes logs to logs/codingbuddy.log.", "timeout": "HTTP timeout in seconds.", "cost": "If enabled, estimates cost by provider and logs it."}}