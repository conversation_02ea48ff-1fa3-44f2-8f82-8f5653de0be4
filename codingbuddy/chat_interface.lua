-- chat_interface.lua - Chat UI interface for CodingBuddy
-- Provides a chat-like interface using available GeanyLua capabilities

local geany = rawget(_G, 'geany')
local conversation_manager = require('codingbuddy.conversation_manager')
local ai_connector = require('codingbuddy.ai_connector')
local dialogs = require('codingbuddy.dialogs')

local M = {}

-- Chat state
local chat_window_open = false
local current_input = ""
local chat_history_display = {}

-- Since GeanyLua has limited GTK bindings, we'll use a hybrid approach:
-- 1. Use geany.message() for simple interactions
-- 2. Create a text-based interface that can be enhanced later
-- 3. Implement the chat logic that can work with better UI when available

local function format_message_for_display(message)
  local timestamp = message.timestamp or os.date('!%Y-%m-%dT%H:%M:%SZ')
  local time_str = timestamp:match('T(%d%d:%d%d)')  -- Extract HH:MM
  local role_prefix = message.role == 'user' and 'You' or 'Assistant'
  
  return string.format("[%s] %s: %s", time_str or '??:??', role_prefix, message.content)
end

local function get_conversation_display()
  local conv = conversation_manager.get_current_conversation()
  local lines = {}
  
  -- Add conversation title and stats
  table.insert(lines, "=== " .. conv.title .. " ===")
  local stats = conversation_manager.get_conversation_stats()
  table.insert(lines, string.format("Messages: %d | Tokens: %d | Cost: $%.4f", 
    stats.message_count, stats.total_tokens, stats.total_cost))
  table.insert(lines, "")
  
  -- Add recent messages (last 10 to keep display manageable)
  local messages = conv.messages
  local start_idx = math.max(1, #messages - 9)  -- Show last 10 messages
  
  if start_idx > 1 then
    table.insert(lines, "... (showing last 10 messages)")
    table.insert(lines, "")
  end
  
  for i = start_idx, #messages do
    table.insert(lines, format_message_for_display(messages[i]))
    table.insert(lines, "")  -- Add spacing between messages
  end
  
  return table.concat(lines, '\n')
end

local function show_chat_window()
  local display = get_conversation_display()
  local prompt = display .. "\n" .. string.rep("-", 50) .. "\n" .. 
                "What can I do for you?\n\n" ..
                "Commands:\n" ..
                "- Type your message and press OK to send\n" ..
                "- Type '/new' to start a new conversation\n" ..
                "- Type '/history' to see conversation list\n" ..
                "- Type '/quit' to close chat\n\n" ..
                "Your message:"
  
  -- Use a simple input dialog for now
  -- In a full GTK implementation, this would be a proper chat window
  if geany and geany.input then
    return geany.input("CodingBuddy Chat", prompt)
  else
    -- Fallback for testing
    print(prompt)
    return io.read()
  end
end

local function process_chat_command(input)
  if not input or input:match('^%s*$') then
    return false  -- Empty input, continue chat
  end
  
  input = input:gsub('^%s+', ''):gsub('%s+$', '')  -- Trim whitespace
  
  if input == '/quit' or input == '/exit' then
    return true  -- Exit chat
  elseif input == '/new' then
    conversation_manager.create_new_conversation()
    dialogs.alert("CodingBuddy", "Started new conversation")
    return false
  elseif input == '/history' then
    local conversations = conversation_manager.list_conversations()
    local history_text = "Recent Conversations:\n\n"
    
    for i, conv in ipairs(conversations) do
      if i <= 10 then  -- Show last 10 conversations
        local date = conv.updated_at:match('(%d%d%d%d%-%d%d%-%d%d)')
        history_text = history_text .. string.format("%d. %s (%s)\n", i, conv.title, date or 'Unknown')
      end
    end
    
    if #conversations == 0 then
      history_text = history_text .. "No previous conversations found."
    end
    
    dialogs.show_text("Conversation History", history_text)
    return false
  else
    -- Regular chat message
    return process_chat_message(input)
  end
end

function process_chat_message(user_input)
  -- Add user message to conversation
  conversation_manager.add_message('user', user_input)
  
  -- Get conversation context for AI
  local context = conversation_manager.get_conversation_context(10)  -- Last 10 messages
  
  -- Prepare AI request using the enhanced ai_connector
  local ai_request = {
    provider = 'anthropic',  -- Default provider
    system = 'You are CodingBuddy, an AI coding assistant integrated into the Geany text editor. ' ..
             'You help with code analysis, generation, debugging, and general programming questions. ' ..
             'Be concise but helpful. If you need to see code, ask the user to share it.',
    messages = context,  -- Use conversation context instead of single prompt
    return_structured = true,
    max_tokens = 2048  -- Allow longer responses for chat
  }

  -- Show loading indicator
  dialogs.alert("CodingBuddy", "Processing your request...")

  -- Make AI request
  local ok, response = pcall(ai_connector.chat, ai_request)

  if not ok then
    local error_msg = "Error communicating with AI: " .. tostring(response)
    conversation_manager.add_message('assistant', error_msg)
    dialogs.alert("CodingBuddy Error", error_msg)
    return false
  end

  -- Handle response
  local ai_text = response
  local metadata = {}

  if type(response) == 'table' then
    ai_text = response.text or response.content or "No response received"
    metadata = {
      tokens = response.usage,
      cost = response.cost,
      model = response.model,
      provider = ai_request.provider
    }
  end
  
  -- Add AI response to conversation
  conversation_manager.add_message('assistant', ai_text, metadata)
  
  return false  -- Continue chat
end

function M.open_chat()
  if not geany then
    print("CodingBuddy Chat Interface")
    print("Running in CLI mode - limited functionality")
  end
  
  chat_window_open = true
  
  -- Main chat loop
  while chat_window_open do
    local user_input = show_chat_window()
    
    if not user_input then
      -- User cancelled or closed dialog
      break
    end
    
    local should_exit = process_chat_command(user_input)
    if should_exit then
      break
    end
  end
  
  chat_window_open = false
  dialogs.alert("CodingBuddy", "Chat session ended")
end

function M.quick_chat(message)
  -- Quick chat function for single interactions
  if not message or message:match('^%s*$') then
    return M.open_chat()
  end
  
  -- Process single message and show result
  conversation_manager.add_message('user', message)

  local context = conversation_manager.get_conversation_context(5)
  local ai_request = {
    provider = 'anthropic',
    system = 'You are CodingBuddy, a helpful coding assistant. Be concise.',
    messages = context,
    return_structured = true,
    max_tokens = 1024
  }

  local ok, response = pcall(ai_connector.chat, ai_request)

  if not ok then
    dialogs.alert("CodingBuddy Error", "Error: " .. tostring(response))
    return
  end

  local ai_text = response
  local metadata = {}
  if type(response) == 'table' then
    ai_text = response.text or response.content or "No response received"
    metadata = {
      tokens = response.usage,
      cost = response.cost,
      model = response.model,
      provider = 'anthropic'
    }
  end

  conversation_manager.add_message('assistant', ai_text, metadata)
  dialogs.show_text("CodingBuddy Response", ai_text)
end

function M.analyze_current_buffer_with_chat()
  -- Enhanced version of the original analyze function that uses chat interface
  local filename, text
  if geany then
    local buf = (geany.buffer and geany.buffer()) or (geany.fileinfo and geany.fileinfo())
    filename = (geany.filename and geany.filename()) or (buf and buf.name) or 'untitled'
    text = (geany.text and geany.text()) or ''
  else
    filename = 'untitled'
    text = ''
  end

  if text == '' then
    dialogs.alert("CodingBuddy", "No code to analyze in current buffer")
    return
  end

  local analysis_prompt = string.format(
    "Please analyze this code file:\n\nFilename: %s\n\nCode:\n```\n%s\n```\n\n" ..
    "Provide a concise analysis focusing on:\n" ..
    "- Code quality and potential issues\n" ..
    "- Suggestions for improvement\n" ..
    "- Any bugs or security concerns",
    filename, text
  )

  M.quick_chat(analysis_prompt)
end

function M.get_chat_status()
  local conv = conversation_manager.get_current_conversation()
  local stats = conversation_manager.get_conversation_stats()

  return {
    active = chat_window_open,
    conversation_id = conv and conv.id or nil,
    message_count = stats and stats.message_count or 0,
    total_tokens = stats and stats.total_tokens or 0,
    total_cost = stats and stats.total_cost or 0
  }
end

return M
